<script>
// @ts-nocheck

	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { fade } from 'svelte/transition';
	import Form from '$lib/Form.svelte';
	import Footer from '$lib/Footer.svelte';
	import Header from '$lib/Header.svelte';
	import RecommendationCardMovies from '$lib/RecommendationCardMovies.svelte';
	import RecommendationCardTvShows from '$lib/RecommendationCardTvShows.svelte';
	import LoadingCard from '$lib/LoadingCard.svelte';
	import LoadingIndicator from '$lib/Loading.svelte';
	import FormButton from '$lib/FormButton.svelte';
	import { specialGenres } from '$lib/constants/SpecialGenres';

	let loading = false;
	let error = '';
	let userData = null;
	let isLoggedIn = false;
	let recommendationCount = 0; // Track recommendation count from server

	/**
	 * @type {string}
	 */
	let searchResponse = '';
	/**
	 * @type {Array<string | {title: string, description: string}>}
	 */
	let recommendations = [];

	/**
	 * @type {string}
	 */
	let cinemaType = 'movie';

	/**
	 * @type {string}
	 */
	let prevCinemaType = cinemaType;

    $: if (cinemaType && prevCinemaType && cinemaType !== prevCinemaType) {
		prevCinemaType = cinemaType;
		clearCache('');
    }

	/**
	 * @type {Array<string>}
	 */
	let selectedCategories = [];

	/**
	 * @type {Array<string>}
	 */
	let prevSelectedCategories = selectedCategories;

	$: if (selectedCategories !== prevSelectedCategories) {
        prevSelectedCategories = selectedCategories;
		clearCache('spgenres');
    }

	/**
	 * @type {string}
	 */
	let specialGenreSelected = '';

	/**
	 * @type {string}
	 */
	let prevSpecialGenreSelected = specialGenreSelected;

	$: if (specialGenreSelected !== prevSpecialGenreSelected) {
		prevSpecialGenreSelected = specialGenreSelected;
		clearCache('genres');
    }

	let userTypedPrompt = '';

	// Global array to track recommended movies
	var alreadyRecommended = [];

	// Prepare system prompt to exclude already recommended movies
	var exclusionRule = ``;

	// Function to fetch current recommendation count from server
	async function fetchRecommendationCount() {
		if (!userData?.email) return;

		try {
			const response = await fetch('/api/getRecommendationCount', {
				method: 'GET',
				headers: {
					'x-user-email': userData.email
				}
			});

			if (response.ok) {
				const data = await response.json();
				recommendationCount = data.count;
			}
		} catch (error) {
			console.error('Error fetching recommendation count:', error);
		}
	}

	// Check for existing login on mount
	onMount(async () => {
		const storedUser = localStorage.getItem('user');
		if (storedUser) {
			try {
				userData = JSON.parse(storedUser);
				isLoggedIn = true;
				// Fetch current recommendation count
				await fetchRecommendationCount();
			} catch (err) {
				localStorage.removeItem('user');
				goto('/');
				return;
			}
		} else {
			// Redirect to login if not logged in
			goto('/');
			return;
		}
	});

	// Handle logout
	function handleLogout() {
		localStorage.removeItem('user');
		goto('/');
	}

	async function search() {
		if (loading) return;
		searchResponse = '';
		loading = true;

		// Send only user selections to server - prompt building happens server-side
		const requestData = {
			cinemaType: cinemaType,
			specialGenreSelected: specialGenreSelected || undefined,
			selectedCategories: selectedCategories || undefined,
			alreadyRecommended: alreadyRecommended.length > 0 ? alreadyRecommended : undefined,
			userCountry: userData?.country_code || 'US'
		};

		console.log('Sending user selections:', requestData);

		const response = await fetch('/api/getRecommendation', {
			method: 'POST',
			body: JSON.stringify(requestData),
			headers: {
				'content-type': 'application/json',
				'x-user-email': userData?.email || ''
			}
		});

		if (response.ok) {
			try {
				const data = await response.text();
				searchResponse = data;
				// Fetch updated recommendation count from server
				await fetchRecommendationCount();
			} catch (err) {
				error = 'You shall not pass... without watching The Lord of the Rings! (Seriously, that would give us enough time to fix this problem 😅)';
			}
		} else {
			const errorText = await response.text();
			if (response.status === 429) {
				error = 'Rate limit exceeded! You can make up to 50 recommendation requests per hour. Please try again later.';
			} else if (response.status === 401) {
				error = 'Authentication required. Please refresh the page and login again.';
			} else {
				error = errorText;
			}
		}

		loading = false;
	}

	$: {
		if (searchResponse) {
			try {
				// Parse the JSON response
				const data = JSON.parse(searchResponse);

				// Ensure the expected structure exists
				if (data.recommendations && Array.isArray(data.recommendations)) {
					let lastLength = recommendations.length;
					let newRecommendations = data.recommendations.map(({ title, year, description }) => ({
						title,
						year,
						description
					}));

					recommendations = [...recommendations, ...newRecommendations];

					// Prepare the alreadyRecommended items to create the exclusionRule so
					// that there is no duplicate recommendations.
					for (const rec of newRecommendations) {
						if (rec.title && rec.year) {
							const entry = `${rec.title} (${rec.year})`;
							if (!alreadyRecommended.includes(entry)) {
								alreadyRecommended.push(entry);
							}
						}
					}

					console.log(alreadyRecommended);

					// Scroll to the beginning of new recommendations
					if (recommendations.length > lastLength) {
						// Use native smooth scrolling to show new content
						setTimeout(() => {
							// Find the first new recommendation card (at the previous length index)
							const recommendationCards = document.querySelectorAll('[data-recommendation-card]');
							if (recommendationCards.length > lastLength) {
								// Scroll to the first new recommendation
								const firstNewCard = recommendationCards[lastLength];
								firstNewCard.scrollIntoView({
									behavior: 'smooth',
									block: 'start',
									inline: 'nearest'
								});
							} else {
								// Fallback: scroll to the last existing card
								const lastCard = recommendationCards[recommendationCards.length - 1];
								if (lastCard) {
									lastCard.scrollIntoView({
										behavior: 'smooth',
										block: 'start',
										inline: 'nearest'
									});
								}
							}
						}, 300); // Delay to ensure content is rendered
					}
				}
			} catch (error) {
				console.error('Error parsing JSON:', error);
			}
		}
	}

	function clearForm() {
		cinemaType = 'movie';
		selectedCategories = [];
		userTypedPrompt = '';
		specialGenreSelected = '';
		clearCache('');
	}

	function clearCache(type) {
		recommendations = [];
		searchResponse = '';
		alreadyRecommended = [];
		exclusionRule = '';
		// Note: Don't reset recommendationCount here as it tracks hourly usage

		if (type == 'genres') {
			selectedCategories = [];
			prevSelectedCategories = [];
		} else if (type == 'spgenres') {
			specialGenreSelected = '';
			prevSpecialGenreSelected = '';
		} else {
			selectedCategories = [];
			prevSelectedCategories = [];
			specialGenreSelected = '';
			prevSpecialGenreSelected = '';
		}
	}
</script>

<svelte:head>
	<title>Movie Recommendations - Cinemated</title>
</svelte:head>

<div>
	<div class="h-screen w-full bg-cover fixed" style="background-image: url(/background.png)">
		<div class="backdrop-blur-md flex flex-col items-center justify-center min-h-screen w-full h-full bg-gradient-to-br from-gray-100/80 to-white/90 dark:from-slate-900/80 dark:to-black/90" />
	</div>

	<div class="absolute inset-0 px-4 md:px-6 flex flex-col h-screen overflor-auto">
		<Header
			{isLoggedIn}
			{userData}
			on:logout={handleLogout}
		/>

		{#if isLoggedIn}
			<div in:fade|global class="w-full max-w-4xl mx-auto">
				<!-- Rate limit indicator -->
				{#if recommendationCount > 0}
					<div class="mb-4 text-center">
						<div class="inline-flex items-center px-3 py-1 rounded-full text-xs {recommendationCount >= 45 ? 'bg-red-500/20 text-red-300' : recommendationCount >= 30 ? 'bg-yellow-500/20 text-yellow-300' : 'bg-green-500/20 text-green-300'}">
							<span class="mr-1">🔒</span>
							{recommendationCount}/50 recommendations used this hour
							{#if recommendationCount >= 45}
								<span class="ml-1">(Almost at limit!)</span>
							{/if}
						</div>
					</div>
				{/if}

				<div class="w-full mb-8">
					<Form
						bind:cinemaType
						bind:selectedCategories
						bind:specialGenreSelected
						on:click={search}
					/>
				</div>
				<div class="max-w-4xl mx-auto w-full">
					{#if loading && !searchResponse && !recommendations}
						<div class="fontsemibold text-lg text-center mt-8 mb-4">
							Please be patient as I think. Good things are coming 😎.
						</div>
					{/if}
					{#if error}
						<div class="fontsemibold text-lg text-center mt-8 text-red-500">
							Woops! {error}
						</div>
					{/if}
					{#if recommendations}
						{#each recommendations as recommendation, i (i)}
							<div>
								{#if recommendation !== ''}
									<!-- Add visual separator for new content rounds -->
									{#if i > 0 && i % 5 === 0}
										<div class="flex items-center my-8">
											<div class="flex-1 h-px bg-gradient-to-r from-transparent via-turquoise-500/30 to-transparent"></div>
											<div class="px-4 text-turquoise-400/70 text-sm font-medium">More Recommendations</div>
											<div class="flex-1 h-px bg-gradient-to-r from-transparent via-turquoise-500/30 to-transparent"></div>
										</div>
									{/if}
									<div class="mb-8" data-recommendation-card>
										{#if typeof recommendation !== 'string' && recommendation.title}
											{#if cinemaType == 'movie'}
												<RecommendationCardMovies {recommendation} userCountry={userData?.country_code || 'US'} />
											{:else}
												<RecommendationCardTvShows {recommendation} userCountry={userData?.country_code || 'US'} />
											{/if}
										{:else}
											<div in:fade|global>
												<LoadingCard incomingStream={false} />
											</div>
										{/if}
									</div>
								{/if}
							</div>
						{/each}
					{/if}
				</div>

				<FormButton
					bind:loading
					bind:recommendations
					{search}
					{clearForm}
				/>

			</div>
		{:else}
			<div class="flex-grow flex items-center justify-center">
				<div class="text-white text-xl">Please wait...</div>
			</div>
		{/if}
		<Footer />
	</div>
</div>
