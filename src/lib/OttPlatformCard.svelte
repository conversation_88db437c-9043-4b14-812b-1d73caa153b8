<script lang="ts">
	import { slide } from 'svelte/transition';
	import { ottPlatforms } from '$lib/constants/OttPlatforms';

	export let selectedOttPlatforms: string[] = [];

	let showAll = false;
	let itemsPerRow = 6; // Show first 6 platforms initially

	function toggleShowAll() {
		showAll = !showAll;
	}

	function togglePlatform(platform: string) {
		if (selectedOttPlatforms.includes(platform)) {
			selectedOttPlatforms = selectedOttPlatforms.filter(p => p !== platform);
		} else {
			selectedOttPlatforms = [...selectedOttPlatforms, platform];
		}
	}
</script>

<div>
	<div class="mb-4 font-semibold text-2xl">
		Choose your streaming platforms (optional)
	</div>
	<div class="mb-2 text-sm text-slate-400">
		Select one or more platforms to get recommendations available on those services
	</div>
	
	<!-- Always show the first row -->
	<div class="flex items-center flex-wrap">
		{#each ottPlatforms.slice(0, itemsPerRow) as platform}
			<label
				class={`btn-ott-platform ${
					selectedOttPlatforms.includes(platform) ? 'bg-turquoise-600/40' : ''
				} text-slate-200 font-bold mr-2 mt-2 text-sm py-2 px-4 rounded-full border border-turquoise-600 cursor-pointer hover:bg-turquoise-600/20 transition-colors`}
			>
				<input
					class="hidden"
					type="checkbox"
					checked={selectedOttPlatforms.includes(platform)}
					on:change={() => togglePlatform(platform)}
				/>
				{platform}
			</label>
		{/each}
	</div>

	{#if ottPlatforms.length > itemsPerRow}
		{#if showAll}
			<div transition:slide>
				<div class="flex items-center flex-wrap mt-2">
					{#each ottPlatforms.slice(itemsPerRow) as platform}
						<label
							class={`btn-ott-platform ${
								selectedOttPlatforms.includes(platform) ? 'bg-turquoise-600/40' : ''
							} text-slate-200 font-bold mr-2 mt-2 text-sm py-2 px-4 rounded-full border border-turquoise-600 cursor-pointer hover:bg-turquoise-600/20 transition-colors`}
						>
							<input
								class="hidden"
								type="checkbox"
								checked={selectedOttPlatforms.includes(platform)}
								on:change={() => togglePlatform(platform)}
							/>
							{platform}
						</label>
					{/each}
				</div>
				<div class="mt-2 flex justify-center">
					<button
						class="btn-show-less-platforms text-turquoise-400 hover:underline focus:outline-none transition-all"
						on:click={toggleShowAll}
						aria-expanded={showAll}
					>
						Show less
					</button>
				</div>
			</div>
		{:else}
			<div class="mt-2 flex justify-center">
				<button
					class="btn-show-more-platforms text-turquoise-400 hover:underline focus:outline-none transition-all"
					on:click={toggleShowAll}
					aria-expanded={showAll}
				>
					Show more platforms
				</button>
			</div>
		{/if}
	{/if}

	{#if selectedOttPlatforms.length > 0}
		<div class="mt-4 p-3 bg-turquoise-600/10 rounded-lg border border-turquoise-600/30">
			<div class="text-sm text-turquoise-300 mb-2">Selected platforms:</div>
			<div class="flex flex-wrap gap-2">
				{#each selectedOttPlatforms as platform}
					<span class="bg-turquoise-600/30 text-turquoise-200 px-2 py-1 rounded text-xs">
						{platform}
						<button
							class="ml-1 text-turquoise-400 hover:text-turquoise-200"
							on:click={() => togglePlatform(platform)}
							aria-label="Remove {platform}"
						>
							×
						</button>
					</span>
				{/each}
			</div>
		</div>
	{/if}
</div>
