import { specialGenres } from '$lib/constants/SpecialGenres';

export interface RecommendationRequest {
	cinemaType: 'movie' | 'tv show';
	specialGenreSelected?: string;
	selectedCategories?: string;
	alreadyRecommended?: string[];
	userCountry: string;
}

export function buildRecommendationPrompt(request: RecommendationRequest): string {
	const { cinemaType, specialGenreSelected, selectedCategories, alreadyRecommended } = request;
	
	let fullSearchCriteria = '';

	// Handle special genre prompts
	if (specialGenreSelected) {
		const specialGenrePrompt = specialGenres.find(
			(genre) => genre.name_slug === specialGenreSelected
		);

		if (specialGenrePrompt) {
			const promptTemplate = cinemaType === 'movie' 
				? specialGenrePrompt.prompt1 
				: specialGenrePrompt.tvprompt1;
			
			fullSearchCriteria += promptTemplate.replaceAll('{{movieType}}', cinemaType);
		}
	} else {
		// Handle regular genre prompts
		if (selectedCategories) {
			fullSearchCriteria += `Give me a list of 5 ${cinemaType} recommendations in the genre "${selectedCategories}".\n`;
			fullSearchCriteria += `If you do not have 5 recommendations that fit these criteria perfectly, do your best to suggest other ${cinemaType}'s that is closer to given description.`;
		} else {
			fullSearchCriteria += `Give me a list of 5 recent ${cinemaType} recommendations.`;
		}
	}

	// Add cinema type restrictions
	if (cinemaType === 'movie') {
		fullSearchCriteria += ` DO NOT include any tv shows.`;
	} else {
		fullSearchCriteria += ` DO NOT include any movies.`;
	}

	// Add exclusion rules for already recommended content
	if (alreadyRecommended && alreadyRecommended.length > 0) {
		const exclusionRule = ` Do NOT include these ${cinemaType} in your response: ${alreadyRecommended.join(", ")}.`;
		fullSearchCriteria += exclusionRule;
	}

	// Add JSON format instructions
	fullSearchCriteria += `
Please return the response in the following JSON format:
{
  "recommendations": [
	{
	  "title": "${cinemaType} title",
	  "year": four-digit year,
	  "description": "short, 1-2 lines ${cinemaType} synopsis or plot"
	}
  ]
}
Ensure that the response is a valid JSON object with properly formatted keys and values.
Provide a list of ${cinemaType} along with their title, release year, and a brief description.
`;

	return fullSearchCriteria;
}
